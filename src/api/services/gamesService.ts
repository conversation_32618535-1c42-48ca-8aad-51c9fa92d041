import {
    IAvailableGamesResponse,
    IGamesGridRow,
    IGamesService,
    IMarketingKitResponse,
    IRoadmapResponse
} from "./interfaces/iGamesService";
import { NextApiRequest } from "next";
import { EnumActivityStatus, EnumUserAction } from "models/enum/user";
import appConfig from "appConfig";
import { IUserActivityParams, IUserInfo, IUserPlayerCodeResponse } from "./interfaces/iUsersService";
import { Game } from "utils/game";
import countries from "data/countries.json";
import * as Errors from "utils/errors";
import { errorCodes } from "errorCodes";
import { SanityApi } from "utils/sanityApi";
import axios, { AxiosResponse } from "axios";
import { ILocaleString } from "../data-providers/sanity/schemas";
import { EnumDBUserGroupCodes } from "models/enum/db";
import { IGSGamePlayResponse, IGSLoginResponse } from "api/services/interfaces/iGameServerService";
import logger from "utils/logger";
import { EnumServerEnv } from "models/enum/system";
import { Tournaments } from "utils/tournamets";
import { ITournamentModel } from "iAppConfig";
import { ISearchData } from "@components/games-search/_interfaces";
import { EnumShowGameCode, EnumShowGameRTP } from "models/enum/game";
import { UsersServiceInstance } from "api/services/usersService";
import { SanityProviderInstance } from "api/data-providers/sanity/sanityProvider";
import { GameServerServiceInstance } from "api/services/gameServerService";
import ServerService from "api/services/serverService";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import { UserActivityCreationAttributes } from "api/data-providers/db/mysql/sequelize/userActivity";
import appServerConfig from "appServerConfig";
import dayjs from "dayjs";
import { SiteServiceInstance } from "./siteService";

const log = logger("service--games");

let getAvailableCache: void | IAvailableGamesResponse;
let getRoadmapCache: void | IRoadmapResponse;
let getSearchCache: void | ISearchData;
const cacheTTL: { [key: string]: number } = { available: 0, roadmap: 0, search: 0 };

export class GamesService extends ServerService implements IGamesService {

    constructor() {
        super();
    }

    async saveFailedActivity(err: unknown,
                             activityAttrs: UserActivityCreationAttributes): Promise<UserActivityCreationAttributes> {
        const attrs = await super.saveFailedActivity(err, activityAttrs);
        if (DBProviderInstance) {
            await DBProviderInstance.activityFailed(attrs, undefined, EnumActivityStatus.serverError);
        }

        return attrs;
    }

    public async getAvailableGames(
        req: NextApiRequest,
        options: { skipAuthorisation?: boolean; gameInfo?: boolean } = {
            skipAuthorisation: false,
            gameInfo: false
        }
    ): Promise<void | IAvailableGamesResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getAvailableGames,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;

            let user = {} as IUserInfo;
            if (!options.skipAuthorisation) {
                user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
                if (!user) {
                    const error = new Errors.BadRequest(errorCodes.e20001001);
                    await DBProviderInstance.activityFailed(activityAttrs, error);
                    return Promise.reject(error);
                }
                activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);
            }

            const nowTimestamp = +new Date();
            if (/*appConfig.environment !== EnumEnvironment.production || */nowTimestamp - cacheTTL.available > 120 * 1000) {
                cacheTTL.available = nowTimestamp;

                const games = await SanityProviderInstance.getAvailableGames(languageCode);

                const page = await SanityProviderInstance.gameMarketsExtended(regionCode, languageCode);

                const providerGames = await DBProviderInstance.getProviderGames(regionCode);

                const pageRoadmap = await SanityProviderInstance.pageRoadmap(appConfig.systemSettings.defaultLanguageCode);

                getAvailableCache = {
                    games: [],
                    availableMarkets: {}
                }

                getAvailableCache.games = games
                    .flatMap((game) => {
                        const gameCode = Game.getFirstGameCode(game);

                        const gameInfoFromBI = Game.getGameInfoFromBI(game);
                        const gameInfo = Game.getGameInfo(game.gameInfo, languageCode);

                        const gameReleaseDate = dayjs(gameInfoFromBI.release_date || gameInfoFromBI.release_date_mga || gameInfo.releaseDate);

                        if(!gameReleaseDate.isValid() || gameReleaseDate.diff(dayjs(), "day") > 1) {
                            return [];
                        }

                        const features =
                            Array.isArray(game?.gameFeatures) &&
                            game?.gameFeatures?.map((f) =>
                                f?.gameFeatureTitle && SanityApi.getLocale(f?.gameFeatureTitle, languageCode)
                            );

                        const gameId = game.gameId?.current;

                        const providerGame = providerGames?.find((game) => game.gameCode === gameCode) || null;

                        const gameVideos = Game.getGameVideos(game);

                        return {
                            id: gameId,
                            gameCode: Game.getGameCodes(game),
                            gamePoster: game.gamePosterUrl || null,
                            gameName: SanityApi.getLocale(game?.gameName, languageCode),
                            releaseDate: gameReleaseDate.isValid() && dayjs(gameReleaseDate).format("YYYY-MM-DD"),
                            year: gameInfoFromBI.release_date ?
                                  dayjs(gameInfoFromBI.release_date).format("YYYY") :
                                  null,
                            markets: gameInfo.markets || gameInfoFromBI.target_market || null,
                            jackpot: providerGame?.isJackpot === 1 ? "true" : "false",
                            features: Array.isArray(features) ? features.join(", ") : null,
                            type: gameInfoFromBI.type || null,
                            types: game.gameTypes?.map(t => t.gameTypeCode?.current) || null,
                            rtp: Game.prepareRTPs(gameInfoFromBI?.theoretical_rtp,
                                game.gameInfo?.rtp,
                                Game.getGameRTPs(game)),
                            vol: gameInfoFromBI.player_volatility || null,
                            tag:
                                String(gameInfoFromBI?.gametags)
                                    .trim()
                                    .split(",")
                                    .map((s) => s.trim().toLowerCase()) || null,
                            cert:
                                String(gameInfoFromBI?.certification)
                                    .trim()
                                    .split(",")
                                    .map((s) => s.trim().toLowerCase()) || null,
                            demoUrl: Game.getGamePlayUrl(
                                gameCode,
                                // game?.paUrl,
                                appConfig.systemSettings.defaultRegionCode,
                                providerGame !== null
                            ),
                            videos: gameVideos,
                            productSheetUrl: game.productSheetUrl || null,
                            infoSheetUrl: game.infoSheetUrl || null,
                            certificates:
                                Game.getGameCertificates(game, languageCode, page.allGameMarkets, page.biggestMarkets)
                                    .certificates || null,
                            languageCode: languageCode,
                            options: {
                                showGameCode: game.showGameCode || EnumShowGameCode.all,
                                showGameRTP: game.showGameRTP || EnumShowGameRTP.from_game_info,
                                formatReleaseDate: gameInfo.formatReleaseDate
                            },
                            ...(options.gameInfo && { gameInfo: gameInfoFromBI })
                        } as IGamesGridRow;
                    })
                    .sort((a: IGamesGridRow, b: IGamesGridRow) => {
                        // Turn your strings into dates, and then subtract them
                        // to get a value that is either negative, positive, or zero.
                        return Number(new Date(b.releaseDate)) - Number(new Date(a.releaseDate));
                    });

                getAvailableCache.availableMarkets =
                    Array.isArray(pageRoadmap?.availableMarkets) && pageRoadmap?.availableMarkets.length > 0
                    ? Object.assign(
                        {},
                        ...pageRoadmap.availableMarkets.map((m) => ({
                            [String(m?.code?.current)]: String(
                                m?.marketName && SanityApi.getLocale(m?.marketName,
                                appConfig.systemSettings.defaultLanguageCode)
                            )
                        }))
                    )
                    : null;
            }
            await DBProviderInstance.activitySuccess(activityAttrs);

            return getAvailableCache;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getAvailableGames", activityAttrs)
            );
        }
    }

    public async getSearchGames(req: NextApiRequest, options: { skipAuthorisation?: boolean; } = {
        skipAuthorisation: false
    }): Promise<void | ISearchData> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getSearchGames,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;

            if (!options.skipAuthorisation) {
                const user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
                if (!user) {
                    const error = new Errors.BadRequest(errorCodes.e20002001);
                    await DBProviderInstance.activityFailed(activityAttrs, error);
                    return Promise.reject(error);
                }
                activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);
            }

            const nowTimestamp = +new Date();
            if (/*appConfig.environment !== EnumEnvironment.production || */nowTimestamp - cacheTTL.search > 120 * 1000) {
                cacheTTL.search = nowTimestamp;

                const searchResponse = await axios.get<unknown, AxiosResponse<ISearchData>>(
                    `https://cdn.skywindgroup.com/search/${regionCode}-${languageCode}.json`,
                    {
                        withCredentials: false
                    }
                );

                const availableGames = (await this.getAvailableGames(req, {
                    skipAuthorisation: true,
                    gameInfo: true
                })) as IAvailableGamesResponse;
                const games = availableGames.games;

                const markets: string[] = [];
                const gameCode2Market: { [gameCode: string]: { [countryName: string]: number } } = {};

                for (let gi = 0; gi < games.length; gi++) {
                    if (games[gi].gameInfo) {
                        const countryGraRank = games[gi].gameInfo?.country_gra_rank;
                        if (countryGraRank && countryGraRank.toString().trim() !== "") {
                            const cr = JSON.parse(countryGraRank);
                            if (Array.isArray(cr) && cr.length > 0) {
                                cr.map((crp: { "Country Code": string; "GRA Rank": string; "GRA Position": string }) => {
                                    const country =
                                        crp["Country Code"] in countries
                                        ? countries[crp["Country Code"] as never]
                                        : crp["Country Code"];
                                    markets.push(country);
                                    gameCode2Market[games[gi].gameCode[0]] = gameCode2Market[games[gi].gameCode[0]] || {};
                                    gameCode2Market[games[gi].gameCode[0]][country] = parseFloat(crp["GRA Rank"]);
                                });
                            }
                        }
                        delete games[gi].gameInfo;
                    }
                }

                const aMarkets = Array.from(new Set(markets));
                aMarkets.sort();

                getSearchCache = {
                    games,
                    search: searchResponse.data.search,
                    countries,
                    markets: aMarkets,
                    gameCode2Market
                } as ISearchData;
            }
            await DBProviderInstance.activitySuccess(activityAttrs);

            return getSearchCache;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getSearchGames", activityAttrs)
            );
        }
    }

    public async getRoadmapGames(req: NextApiRequest): Promise<void | IRoadmapResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getRoadmapGames,
            { params: {} } as IUserActivityParams,
            req
        );

        try {
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const regionCode = appConfig.systemSettings.defaultRegionCode;
            const user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e20003001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            const nowTimestamp = +new Date();
            if (/*appConfig.environment !== EnumEnvironment.production || */nowTimestamp - cacheTTL.roadmap > 120 * 1000) {
                cacheTTL.roadmap = nowTimestamp;

                const pageRoadmap = await SanityProviderInstance.pageRoadmap(appConfig.systemSettings.defaultLanguageCode);

                const raStartDate = dayjs().startOf("month");

                const roadmapGames = Array.isArray(pageRoadmap?.roadmapGame) ? pageRoadmap.roadmapGame : [];

                const providerGames = await DBProviderInstance.getProviderGames(regionCode);

                getRoadmapCache = {
                    showPerMonthToolbar: Boolean(pageRoadmap.showPerMonthToolbar),
                    defaultMarketCode: String(pageRoadmap.defaultMarket?.code?.current),
                    availableMarkets: {},
                    biggestMarkets: {},
                    roadmapMonths: {},
                    roadmapMarkets: {},
                    games: {},
                    limit: {
                        latestReleasesMonth: pageRoadmap.latestReleasesMonth || 6,
                        upcomingMonthThirdParty: pageRoadmap.upcomingMonthThirdParty || 2
                    }
                };
                //
                // const gameCertificates = await this.getCertificates(languageCode);

                const is3P = user.userPermissions.groups?.includes(EnumDBUserGroupCodes.partnerThirdParty);

                if (Array.isArray(roadmapGames) && roadmapGames.length > 0) {
                    for (const roadmapGame of roadmapGames) {
                        const gameMarkets = roadmapGame?.roadmapGameMarket;

                        const gameCode = Game.getFirstGameCode(roadmapGame.game);
                        if (gameCode && Array.isArray(gameMarkets) && gameMarkets.length > 0) {
                            for (const market of gameMarkets) {
                                const marketStartDate = new Date(market?.startDate);
                                const marketYearMonth =
                                    marketStartDate.getFullYear() * 100 + (marketStartDate.getMonth() + 1);
                                if (marketYearMonth < parseInt(raStartDate.format("YYYYMM"), 10)) {
                                    continue;
                                }

                                if (
                                    is3P &&
                                    dayjs(marketStartDate).diff(raStartDate, "months", true) >
                                    getRoadmapCache.limit.upcomingMonthThirdParty
                                ) {
                                    continue;
                                }

                                const marketCode: string = String(market?.gameMarket?.code?.current);
                                getRoadmapCache.roadmapMarkets[marketCode] = {
                                    key: marketCode,
                                    text: market?.gameMarket?.marketName?.en as string,
                                    iconUrl: market?.gameMarket.iconUrl
                                };
                                if (!getRoadmapCache.games[marketCode]) {
                                    getRoadmapCache.games[marketCode] = {};
                                }
                                if (!getRoadmapCache.games.all_markets) {
                                    getRoadmapCache.games.all_markets = {};
                                }
                                const gameId = String(roadmapGame?.game?.gameId?.current);
                                const hideInAllMarkets = roadmapGame?.hideInAllMarkets;
                                if (!getRoadmapCache.games[marketCode][gameId]) {
                                    const game = roadmapGame.game;

                                    let gameInfo = Game.getGameInfoFromBI(game);

                                    const providerGame = providerGames?.find((game) => game.gameCode === gameCode) || null;

                                    const features =
                                        Array.isArray(game?.gameFeatures) &&
                                        game?.gameFeatures?.map(
                                            (f) => f?.gameFeatureTitle && SanityApi.getLocale(f?.gameFeatureTitle,
                                                languageCode)
                                        );

                                    const rd = dayjs(
                                        getRoadmapCache.games.all_markets && getRoadmapCache.games.all_markets[gameCode]?.releaseDate
                                        ? getRoadmapCache.games.all_markets[gameCode]?.releaseDate
                                        : market?.startDate,
                                        "YYYY-M-D"
                                    );
                                    const sd = dayjs(market?.startDate, "YYYY-M-D");

                                    const certificates = Game.getGameCertificates(
                                        game,
                                        languageCode,
                                        pageRoadmap.allGameMarkets,
                                        pageRoadmap.biggestMarkets
                                    ).certificates;

                                    const vol = gameInfo.player_volatility || game.gameInfo?.volatility;

                                    const gameVideos = Game.getGameVideos(game);

                                    const row = {
                                        id: gameId,
                                        gameCode: Game.getGameCodes(game),
                                        gamePoster: game.gamePosterUrl,
                                        gameName: SanityApi.getLocale(game?.gameName as ILocaleString, languageCode),
                                        releaseDate: sd.format("YYYY-MM-DD"),
                                        markets:
                                            SanityApi.getLocale(
                                                market?.gameMarket?.marketName as ILocaleString,
                                                languageCode
                                            ) || null,
                                        features: Array.isArray(features) ? features.join(", ") : null,
                                        rtp: Game.prepareRTPs(
                                            gameInfo?.theoretical_rtp,
                                            game.gameInfo?.rtp,
                                            Game.getGameRTPs(game)
                                        ),
                                        vol,
                                        demoUrl: Game.getGamePlayUrl(
                                            gameCode,
                                            // game.paUrl,
                                            appConfig.systemSettings.defaultRegionCode,
                                            providerGame !== null
                                        ),
                                        videos: gameVideos,
                                        productSheetUrl: game.productSheetUrl,
                                        infoSheetUrl: game.infoSheetUrl,
                                        certificates: certificates || null,
                                        languageCode: languageCode,
                                        options: {
                                            showGameCode: game.showGameCode || EnumShowGameCode.all,
                                            showGameRTP: game.showGameRTP || EnumShowGameRTP.from_game_info,
                                            formatReleaseDate: game.gameInfo?.formatOfReleaseDate || null
                                        }
                                    } as IGamesGridRow;
                                    getRoadmapCache.games[marketCode][gameId] = row;

                                    if (hideInAllMarkets !== true) {
                                        getRoadmapCache.games["all_markets"][gameId] = {
                                            ...row,
                                            releaseDate:
                                                rd.isValid() && rd.format("x") < sd.format("x")
                                                ? rd.format("YYYY-MM-DD")
                                                : sd.format("YYYY-MM-DD"),
                                            markets:
                                                (getRoadmapCache.games.all_markets[gameCode]
                                                 ? getRoadmapCache.games.all_markets[gameCode].markets + ", "
                                                 : "") + market?.gameMarket?.marketName?.en
                                        } as IGamesGridRow;
                                    }
                                }
                                getRoadmapCache.roadmapMonths[marketYearMonth] = marketYearMonth;

                            }
                        }
                    }
                    // response.months = Object.values(response.roadmapMonths);
                }

                if (typeof getRoadmapCache.games === "object") {
                    for (const market in getRoadmapCache.games) {
                        const games = Object.values(getRoadmapCache.games[market]);
                        games.sort((a: IGamesGridRow, b: IGamesGridRow) => {
                            // Turn your strings into dates, and then subtract them
                            // to get a value that is either negative, positive, or zero.
                            return Number(new Date(a.releaseDate)) - Number(new Date(b.releaseDate));
                        });
                        getRoadmapCache.games[market] = {};
                        for (const game of games) {
                            getRoadmapCache.games[market][game.id] = game;
                        }
                    }
                }

                getRoadmapCache.availableMarkets =
                    Array.isArray(pageRoadmap?.availableMarkets) && pageRoadmap?.availableMarkets.length > 0
                    ? Object.assign(
                        {},
                        ...pageRoadmap.availableMarkets.map((m) => ({
                            [String(m?.code?.current)]: String(
                                m?.marketName && SanityApi.getLocale(m?.marketName,
                                    appConfig.systemSettings.defaultLanguageCode)
                            )
                        }))
                    )
                    : null;

                getRoadmapCache.biggestMarkets =
                    Array.isArray(pageRoadmap?.biggestMarkets) && pageRoadmap?.biggestMarkets.length > 0
                    ? Object.assign(
                        {},
                        ...pageRoadmap.biggestMarkets.map((m) => ({
                            [String(m?.code?.current)]: String(
                                m?.marketName && SanityApi.getLocale(m?.marketName,
                                    appConfig.systemSettings.defaultLanguageCode)
                            )
                        }))
                    )
                    : null;
            }

            await DBProviderInstance.activitySuccess(activityAttrs);

            return getRoadmapCache;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getRoadmapGames", activityAttrs)
            );
        }
    }

    public async getGamePromoUrl(req: NextApiRequest): Promise<void | string> {

        const tournaments = Tournaments.getCurrentlyUsed({ filter: { isActive: true } });
        const tournament = (tournaments.length > 0 && tournaments[0]) || ({} as ITournamentModel);

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getPromoGameUrl,
            { params: { gameCode: tournament?.gameCode, type: "tournament" } } as IUserActivityParams,
            req
        );
        try {
            if (
                (!Tournaments.isEnabled() ||
                    !Tournaments.isTournamentPlayEnabled(tournament) ||
                    tournaments.length === 0) &&
                appServerConfig.serverEnv === EnumServerEnv.production
            ) {
                const error = new Errors.BadRequest(errorCodes.e20004003);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            const userPlayer = (await UsersServiceInstance.getPlayerCode(req)) as IUserPlayerCodeResponse;
            if (!userPlayer || !userPlayer.isValid) {
                const error = new Errors.BadRequest(errorCodes.e20004001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e20004002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            const loginResponse = await GameServerServiceInstance.login();
            if (axios.isAxiosError(loginResponse) || !(loginResponse as IGSLoginResponse).accessToken) {
                const error = new Errors.BadRequest(errorCodes.e20004005);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            const game = (await GameServerServiceInstance.getGameUrl(
                userPlayer.playerCode as string,
                tournament.gameCode
            )) as IGSGamePlayResponse;
            if (axios.isAxiosError(game)) {
                const error = new Errors.BadRequest(errorCodes.e20004004);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySuccess(activityAttrs);
            return game.url;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getGamePromoUrl", activityAttrs)
            );
        }
    }

    public async getFunGameUrl(req: NextApiRequest): Promise<void | string> {
        try {
            const game = (await SiteServiceInstance.getGameUrl(
                tournament.gameCode as string
            )) as ISiteGameUrlResponse;
            if (axios.isAxiosError(game)) {
                const error = new Errors.BadRequest(errorCodes.e20004004);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            await DBProviderInstance.activitySuccess(activityAttrs);
            return game.url;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getGamePromoUrl", activityAttrs)
            );
        }
    }

    public async getMarketingKit(req: NextApiRequest, gameId: string): Promise<void | IMarketingKitResponse> {

        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getMarketingKit,
            { params: {} } as IUserActivityParams,
            req
        );
        try {
            const languageCode = appConfig.systemSettings.defaultLanguageCode;
            const user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e20005001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            const game = await SanityProviderInstance.getGameProfile(String(gameId), languageCode);
            if (!game) {
                const error = new Errors.BadRequest(errorCodes.e20005002);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            const marketingMaterials = Array.isArray(game.gameCodes) && game.gameCodes.length > 0;

            let res;
            try {
                res = await axios.get(`https://storage.googleapis.com/lobby.stg1.m27613.com/gamestudios/${game.gameCodes[0].gameCode}/info.json`);
            } catch (e) {
            }

            const marketingMaterialsJson = res?.status === 200 && res?.data && res?.data?.marketingKit;

            if (!game || !marketingMaterials || !marketingMaterialsJson) {
                const error = new Errors.NotFound(errorCodes.e20005003);
                activityAttrs.game_code = gameId;
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            await DBProviderInstance.activitySuccess(activityAttrs);

            return res?.data;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "GamesService.getMarketingKit", activityAttrs)
            );
        }
    }
}

export const GamesServiceInstance = new GamesService();