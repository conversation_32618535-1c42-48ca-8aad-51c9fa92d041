"build": "next build",
"dev": "NODE_OPTIONS='--inspect' gulp bump && next dev -p 3044",
"start": "next start -p 3044",

    "dev": "NODE_OPTIONS='--inspect' gulp bump && next dev -p 3044",
    "build": "next build && tsc --project tsconfig.server.json",
    "start": "cross-env NODE_ENV=production node dist/server/index.js",

This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

# Partner Area version 2

1. project use https://sanity.io as main storage
2. second storage is mysql DB
3. used [ReactJS](https://reactjs.org/) and [NextJS](https://nextjs.org/) as server
4. cdn as main public storage

# test localhost 
```bash
lt --port 3044 --print-requests --local-host eu.skywindgroup.localhost
```

# Install on local end

1. Get project from https://bitbucket.skywindgroup.com/projects/BYSWIP/repos/sw-partner-area/browse
2. Need create env.local file with correct variables in the source root. env.example can be used as example
3. Download and install mysql 5
4. Create db from backup and user in the mysql
5. Add to host file following domains:
   127.0.0.1 partnerarea.skywindgroup.localhost
   127.0.0.1 eu.skywindgroup.localhost
   127.0.0.1 asia.skywindgroup.localhost
   127.0.0.1 asiaskywind.localhost
6. Download and install https://nodejs.org/
7. install Yarn package manager https://yarnpkg.com/getting-started/install
8. Run yarn install yarn dev to run project
   Partner area can be accessed by http://partnerarea.skywindgroup.localhost:3044/
   Website can be accessed by http://eu.skywindgroup.localhost:3044/

# Folder details
   - cypress - tests
   - db - initial mysql scripts
   - public - all public files including assets, js libraries, compiled styles and landing pages
   - server - temporary server to support crons
   - src - api and client sources
   - styles - scss sources for pa and site. Compiled by gulp task: gulp 

## DEV Logs

https://logs-eu-ro-onprem-preprod.internal.skywindservices.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-1h,to:now))&_a=(columns:!(host,short_message,type),filters:!(),index:'6bdd0c70-c91f-11eb-95f6-d91929d52c73',interval:auto,query:(language:kuery,query:%22partner-area-service%22),sort:!())

https://logs-eu-ro-onprem-preprod.internal.skywindservices.com/app/discover#/?_g=(filters:!(),refreshInterval:(pause:!t,value:0),time:(from:now-15m,to:now))&_a=(columns:!(_source),filters:!(),index:d3be65d0-ff27-11eb-b896-6752a4db11a3,interval:auto,query:(language:kuery,query:''),sort:!())

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
```

Open [http://localhost:3044](http://localhost:3044) with your browser to see the result.

You can start editing the page by modifying `pages/index.tsx`. The page auto-updates as you edit the file.

[API routes](https://nextjs.org/docs/api-routes/introduction) can be accessed on [http://localhost:3000/api/hello](http://localhost:3000/api/hello). This endpoint can be edited in `pages/api/hello.tsx`.

The `pages/api` directory is mapped to `/api/*`. Files in this directory are treated as [API routes](https://nextjs.org/docs/api-routes/introduction) instead of React pages.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Docker
```sh
docker build --no-cache --progress=plain -f Dockerfile.prod -t sw-partner-area .
docker run -p 8080:3044 -it sw-partner-area
```
Open [https://localhost:8080/](https://localhost:8080/)

## Generate DB models
```sh
sequelize-auto --host localhost --database local-skywindgroup-com --user root --port 3306 --output "src/api/data-providers/db/mysql/sequelize" --dialect mysql --caseModel p --caseFile c --caseProp o --lang ts
```

## Generate Sanity Schemas
1. install plugin graphql into webstorm
2. create file .graphqlconfig
```json
{
  "name": "Sanity GraphQL Schema",
  "schemaPath": "schema.graphql",
  "extensions": {
    "endpoints": {
      "Default GraphQL Endpoint": {
        "url": "https://usfaerfs.api.sanity.io/v1/graphql/production/default",
        "headers": {
          "user-agent": "JS GraphQL",
          "Authorization": "Bearer TOKEN"
        },
        "introspect": false
      }
    }
  }
}
```
3. replace TOKEN with access token generated in sanity
4. go to sanity desktop project 
5. run command 
```shell
sanity graphql deploy
```
6. return to current project and generate schema file .schema.graphql using GraphQL plugin in Webstorm
7. undeploy graphqlapi from sanity project 
```shell
sanity graphql undeploy
```
8. generate typescript interface using command in current project
```shell
graphql-schema-typescript generate-ts ./schema.graphql --typePrefix I --output "./src/api/data-providers/sanity/schemas.ts"
```


## Web Services client
### Installation https://github.com/OpenAPITools/openapi-generator
1. download and install the latest version of java SE
```shell
https://www.oracle.com/technetwork/java/index.html
```
2. To install the tool as a dev dependency in your current project:
```shell
yarn add -D @openapitools/openapi-generator-cli
```
3. Check version
```shell
npx @openapitools/openapi-generator-cli version
```
4. Generate
```shell
npx @openapitools/openapi-generator-cli generate -g typescript-axios --model-name-prefix I -i http://web-services.localhost:3080/api/docs-json -o ./src/api/data-providers/web-services/client-api
Get-ChildItem ./src/api/data-providers/web-services/client-api -Exclude *.ts | Remove-Item
```
