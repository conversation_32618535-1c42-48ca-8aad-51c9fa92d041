import { error400, error401, error403, error404 } from "utils/errors";
import { EnumError } from "locales/enumLocale";

export const errorCodes = {
    /** Common */
    e100403: error403(100403, EnumError.userPermissionDenied),
    /** Controllers **/
    /** UsersController **/
    e11001001: 11001001 /** @see UsersController.register **/,
    e11002001: 11002001 /** @see UsersController.login **/,
    e11003001: 11003001 /** @see UsersController.logout **/,
    e11004001: 11004001 /** @see UsersController.getUsers **/,
    e11005001: 11005001 /** @see UsersController.activate **/,
    e11006001: 11006001 /** @see UsersController.refresh **/,
    e11006002: error401(11006002, EnumError.userRefreshTokenNotFound) /** @see UsersController.refresh **/,
    e11007001: 11007001 /** @see UsersController.setPlayerCode **/,
    e11008001: 11008001 /** @see UsersController.getPlayerCode **/,
    e11009001: 11009001 /** @see UsersController.changePassword **/,
    e11010001: 11010001 /** @see UsersController.saveAttribute **/,
    e11011001: 11011001 /** @see UsersController.saveUserClientAction **/,
    /** GamesController **/
    e12001001: 12001001 /** @see GamesController.getAvailable **/,
    e12002001: 12002001 /** @see GamesController.getRoadmap **/,
    e12003001: 12003001 /** @see GamesController.getAvailable **/,
    e12004001: 12004001 /** @see GamesController.getPlayPromo **/,
    e12005001: 12005001 /** @see GamesController.getMarketingKit **/,
    /** ProjectController **/
    e13001001: 13001001 /** @see ProjectController.getHealth **/,
    e13002001: 13002001 /** @see ProjectController.getNewsList **/,
    e13003001: 13003001 /** @see ProjectController.getWebsiteFooter **/,
    e13004001: 13004001 /** @see ProjectController.getNewsInfo **/,
    e13005001: 13005001 /** @see ProjectController.getGitbookSpaceUrl **/,
    /** SiteController **/
    e12007001: 12007001 /** @see SiteController.getFunGameUrl **/,
    /** StorageController **/
    e********: ******** /** @see StorageController.getNestList **/,
    e********: ******** /** @see StorageController.getNewsInfoGames **/,
    e********: ******** /** @see StorageController.getNewsInfoMarket **/,
    e********: ******** /** @see StorageController.getNewsInfoPartnership **/,
    /**
     * Services
     **/
    /**
     * @see UsersService
     * */
    /** @see UsersService.register */
    e********: error400(********, EnumError.anAccountWithThisEmailAlreadyExists),
    e********: error400(********, EnumError.anAccountWithThisEmailAlreadyExists),
    /** @see UsersService.login */
    e********: error404(********, EnumError.userIsNotFound),
    e********: error400(********, EnumError.userNotActivated),
    e********: error400(********, EnumError.wrongPassword),
    /** @see UsersService.activateUser */
    e********: error404(********, EnumError.userActivationNotFound),
    /** @see UsersService.logout */
    e********: error404(********, EnumError.userRefreshTokenNotFound),
    /** @see UsersService.changePassword */
    e********: error400(********, EnumError.userNotFoundOrDisabled),
    e********: error400(********, EnumError.newPasswordDoesntMatchCurrent),
    /** @see UsersService.saveAttribute */
    e********: error400(********, EnumError.userNotFoundOrDisabled),
    /** @see UsersService.getAuthorisedUser */
    e********: error400(********, EnumError.userInAccessToken),
    e********: error400(********, EnumError.userNotFoundOrDisabled),
    /** @see UsersService.setPlayerCode */
    e10008001: error400(10008001, EnumError.userNotFoundOrDisabled),
    e10008002: error400(10008002, EnumError.playerCodeAlreadySet),
    e10008003: error400(10008003, EnumError.cantConnectToGameServer),
    e10008004: error400(10008004, EnumError.invalidPlayerCode),
    e10008005: error400(10008005, EnumError.playerCodeExists),
    e10008006: error400(10008006, EnumError.impossibleSetPlayerBalance),
    e10008007: error400(10008007, EnumError.tournamentDisabledOrEndedOrNotStarted),
    e10008008: error400(10008008, EnumError.tournamentDisabledOrEndedOrNotStarted),
    /** @see UsersService.getPlayerCode */
    e10009001: error400(10009001, EnumError.userNotFoundOrDisabled),
    e10009002: error400(10009002, EnumError.tournamentDisabledOrEndedOrNotStarted),
    e10009003: error400(10009003, EnumError.tournamentDisabledOrEndedOrNotStarted),
    /** @see UsersService.refreshToken */
    e10010001: error400(10010001, EnumError.userNotFoundOrDisabled),
    /** @see UsersService.registerUserClientAction */
    e10011001: error400(10011001, EnumError.userNotFoundOrDisabled),

    /**
     * @see GamesService
     * */
    /** @see GamesService.getAvailableGames */
    e20001001: error400(20001001, EnumError.userNotFoundOrDisabled),
    /** @see GamesService.getSearchGames */
    e20002001: error400(20002001, EnumError.userNotFoundOrDisabled),
    /** @see GamesService.getRoadmapGames */
    e20003001: error400(20003001, EnumError.userNotFoundOrDisabled),
    /** @see GamesService.getGamePromoUrl */
    e20004001: error400(20004001, EnumError.userNotFoundOrDisabledOrNoPlayerCode),
    e20004002: error400(20004002, EnumError.userNotFoundOrDisabled),
    e20004003: error400(20004003, EnumError.tournamentDisabledOrEndedOrNotStarted),
    e20004004: error400(20004004, EnumError.cantGenerateGameUrl),
    e20004005: error400(20004005, EnumError.cantConnectToGameServer),
    /** @see GamesService.getMarketingKit */
    e20005001: error400(20005001, EnumError.userNotFoundOrDisabled),
    e20005002: error400(20005002, EnumError.gameNotFound),
    e20005003: error404(20005003, EnumError.marketingMaterialsNotFound),
    /** @see GamesService.getGitbookSpaceUrl */
    e20006001: error400(20006001, EnumError.userNotFoundOrDisabled),
    e20006002: error404(20006002, EnumError.gitbookSpaceNotFound),
    /** @see SiteService.getFunGameUrl */
    e20007001: error400(20007001, EnumError.userNotFoundOrDisabled),
    e20007002: error400(20007002, EnumError.gameCodeRequired),
    e20007003: error404(20007003, EnumError.gameUrlNotFound),
    /**
     * @see ProjectService
     * */
    /** @see ProjectService.getNewsList */
    e30001001: error400(30001001, EnumError.userNotFoundOrDisabled),
    /** @see ProjectService.getWebsiteConfig */
    e30002001: error400(30002001, EnumError.userNotFoundOrDisabled),
    /** @see ProjectService.getNewsArticle */
    e30003001: error400(30003001, EnumError.userNotFoundOrDisabled),
    e30003002: error404(30003002, EnumError.contentNotFound),
};
