import { EnumUserClientAction, EnumUserClientArea } from "models/enum/user";
import { isMobile } from "react-device-detect";
import { ClientRequest } from "utils/clientRequest";
import ClientUsersService from "client/services/clientUsersService";
import { ClientApp } from "client/app/clientApp";
import AppStore from "@stores/app-store";

export class Actions {
    static playGameInDialogIframe = async (
        gameCode: string | null,
        gameName: string,
        gameUrl: string,
        actionName: EnumUserClientArea,
        saveAction: boolean
    ): Promise<void> => {
        if (isMobile) {
            ClientRequest.openInNewTab(gameUrl);
        } else {
            const msg = ClientApp.locale.messages();
            ClientApp.dialog.openIframe(msg.titlePlayGame + gameName, gameUrl);
        }
        if (saveAction) {
            await ClientUsersService.clientAction(
                EnumUserClientAction.gamePlay,
                location.pathname,
                {
                    projectType: AppStore.getAppProject().type,
                    gameCode: gameCode,
                    title: gameName,
                    url: gameUrl
                },
                actionName
            );
        }
    };
}
