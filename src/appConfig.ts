import { IConfigGameServer, IConfigProjects, ITournamentsConfig } from "iAppConfig";
import {
    EnumCurrencyCode,
    EnumGameServerEntityType,
    EnumGameServerPlayMode,
    EnumLanguageCode,
    EnumProjectCode,
    EnumProjectType,
    EnumRegionCode,
    EnumSeoCanonicalDomain,
    EnumServerEnv
} from "models/enum/system";

const hashidsAlphabet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";

const appConfig = {
    constant: {
        salesEmail: "salesEmail",
        contactPhone: "contactPhone"
    },

    projects: <IConfigProjects>{
        pa: {
            code: EnumProjectCode.pa,
            googleAnalyticsTracingCode: "G-QBGS86BZTN",
            name: "Partner Area",
            type: EnumProjectType.pa,
            canonicalDomain: EnumSeoCanonicalDomain.pa,
            domains: [
                {
                    hostname: "partnerarea.skywindgroup.localhost",
                    environment: EnumServerEnv.local
                },
                {
                    hostname: "dev-partnerarea.skywindgroup.com",
                    environment: EnumServerEnv.develop
                },
                {
                    hostname: EnumSeoCanonicalDomain.pa,
                    environment: EnumServerEnv.production
                }
            ]
        },
        eu: {
            code: EnumProjectCode.website_eu,
            googleAnalyticsTracingCode: "G-XW5MECRDMK",
            name: "Website Europe",
            type: EnumProjectType.site,
            canonicalDomain: EnumSeoCanonicalDomain.website_eu,
            domains: [
                {
                    hostname: "eu.skywindgroup.localhost",
                    environment: EnumServerEnv.local
                },
                {
                    hostname: "dev-eu-website.skywindgroup.com",
                    environment: EnumServerEnv.develop
                },
                {
                    hostname: EnumSeoCanonicalDomain.website_eu,
                    environment: EnumServerEnv.production
                },
                {
                    hostname: "www." + EnumSeoCanonicalDomain.website_eu,
                    environment: EnumServerEnv.production
                },
                {
                    hostname: "eu.skywindgroup.com",
                    environment: EnumServerEnv.production
                },
                {
                    hostname: "www.skywindportal.com",
                    environment: EnumServerEnv.production
                },
                {
                    hostname: "skywindportal.com",
                    environment: EnumServerEnv.production
                }
            ]
        },
        china: {
            code: EnumProjectCode.website_china,
            googleAnalyticsTracingCode: "G-XPWP1BL7GY",
            name: "Website China",
            type: EnumProjectType.site,
            canonicalDomain: EnumSeoCanonicalDomain.website_china,
            domains: [
                {
                    hostname: "asia.skywindgroup.localhost",
                    environment: EnumServerEnv.local
                },
                {
                    hostname: "dev-china-website.skywindgroup.com",
                    environment: EnumServerEnv.develop
                },
                {
                    hostname: EnumSeoCanonicalDomain.website_china,
                    environment: EnumServerEnv.production
                }
            ]
        },
        asia: {
            code: EnumProjectCode.website_asia,
            googleAnalyticsTracingCode: "G-JZ6PHJSJ2P",
            name: "Website Asia",
            type: EnumProjectType.site,
            canonicalDomain: EnumSeoCanonicalDomain.website_asia,
            domains: [
                {
                    hostname: "asiaskywind.localhost",
                    environment: EnumServerEnv.local
                },
                {
                    hostname: "dev-asia-website.skywindgroup.com",
                    environment: EnumServerEnv.develop
                },
                {
                    hostname: EnumSeoCanonicalDomain.website_asia,
                    environment: EnumServerEnv.production
                },
                {
                    hostname: "www." + EnumSeoCanonicalDomain.website_asia,
                    environment: EnumServerEnv.production
                }
            ]
        }
    },

    tournaments: <ITournamentsConfig>{
        isActive: true,
        currentlyUsed: [{ code: "tellerOfTales" }],
        storage: [
            {
                isActive: true,
                gameId: "sw_mmu",
                code: "nunchucksChicken",
                gameCode: "sw_mmu_965_partner",
                startDate: "2022-02-01T00:00:00.000Z", // UTC
                endDate: "2022-02-14T07:00:00.000Z", // UTC
                timerStartDate: "2022-02-07T05:00:00.000Z", // UTC
                timerEndDate: "2022-02-11T16:00:00.000Z", // UTC
                playStartDate: "2022-02-04T05:00:00.000Z", // UTC
                playEndDate: "2022-02-14T07:00:00.000Z", // UTC
                gameServerSecretKey: "b0685b57-cedc-4385-99ef-013691e4d174"
            },
            {
                isActive: true,
                gameId: "sw_tot",
                code: "tellerOfTales",
                gameCode: "sw_tot_965_partner",
                startDate: "2022-03-18T00:00:00.000Z", // UTC
                endDate: "2022-04-08T13:00:00.000Z", // UTC
                timerStartDate: "2022-04-04T05:00:00.000Z", // UTC
                timerEndDate: "2022-04-08T13:00:00.000Z", // UTC
                playStartDate: "2022-03-30T05:00:00.000Z", // UTC
                playEndDate: "2022-04-08T13:00:00.000Z", // UTC
                gameServerSecretKey: "b0685b57-cedc-4385-99ef-013691e4d174"
            }
        ]
    },

    dataImage: {
        // https://png-pixel.com/
        i428x268:
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAawAAAEMCAQAAAAy3UrJAAACLElEQVR42u3TQREAAAjDsE051jHBj0RC79pMgGM1FhgLjAXGAowFxgJjAcYCY4GxAGOBscBYgLHAWGAswFhgLDAWYCwwFhgLMBYYC4wFGAuMBcYCjAXGAmOBsQBjgbHAWICxwFhgLMBYYCwwFmAsMBYYCzAWGAuMBRgLjAXGAowFxgJjAcYCY4GxAGOBscBYYCzAWGAsMBZgLDAWGAswFhgLjAUYC4wFxgKMBcYCYwHGAmOBsQBjgbHAWICxwFhgLMBYYCwwFhgLMBYYC4wFGAuMBcYCjAXGAmMBxgJjgbEAY4GxwFiAscBYYCzAWGAsMBZgLDAWGAswFhgLjAXGMhYYC4wFxgKMBcYCYwHGAmOBsQBjgbHAWICxwFhgLMBYYCwwFmAsMBYYCzAWGAuMBRgLjAXGAowFxgJjgbEAY4GxwFiAscBYYCzAWGAsMBZgLDAWGAswFhgLjAUYC4wFxgKMBcYCYwHGAmOBsQBjgbHAWGAswFhgLDAWYCwwFhgLMBYYC4wFGAuMBcYCjAXGAmMBxgJjgbEAY4GxwFiAscBYYCzAWGAsMBYYCzAWGAuMBRgLjAXGAowFxgJjAcYCY4GxAGOBscBYgLHAWGAswFhgLDAWYCwwFhgLMBYYC4wFxhIBjAXGAmMBxgJjgbEAY4GxwFiAscBYYCzAWGAsMBZgLDAWGAswFhgLjAUYC4wFxgKMBcYCY4GxjAXGAmOBsQBjgbHAWICxwFjwwwIWdYYBkD6hmwAAAABJRU5ErkJggg==",
        i1x1: "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="
    },

    client: {
        endpoints: {
            pa: {
                default: "/roadmap"
            },
            external: {
                skywind: {
                    mainWebsite: "//skywindgroup.com",
                    registrationUrl: "https://registers.gamblingcommission.gov.uk/52261",
                    careers: "https://careers.skywindgroup.com/",
                    partnerArea: "https://partnerarea.skywindgroup.com/",
                    policy: "https://skywindgroup.com/en/policy",
                    cookies: "https://skywindgroup.com/en/cookies",
                    contactUs: "https://skywindgroup.com/en/contact-us"
                    // salesEmail: "<EMAIL>",
                    // contactPhone: "+35722053051",
                    // instagramUrl: "https://www.instagram.com/skywind.group/",
                    // facebookUrl: "https://www.facebook.com/SkywindGroup/",
                    // tiktokUrl: "https://www.tiktok.com/@skywind_group?lang=en",
                    // twitterUrl: "https://twitter.com/skywind_group?lang=en",
                    // youtubeUrl: "https://www.youtube.com/c/SkywindGroup",
                    // linkedinUrl: "https://www.linkedin.com/company/skywind-group/",
                    // giphyUrl: "https://giphy.com/Skywindgroup",
                }
            },
            api: {
                users: {
                    register: "/users/register",
                    activate: "/users/activate/:activationHash",
                    login: "/users/login",
                    changePassword: "/users/change-password",
                    saveAttribute: "/users/save-attribute",
                    logout: "/users/logout",
                    refresh: "/users/refresh",
                    playerCode: "/users/player-code",
                    profile: "/users/profile",
                    clientAction: "/users/client-action"
                },
                games: {
                    totalJackpot: "/games/total/jackpot/:projectCode/:currencyCode",
                    available: "/games/available",
                    search: "/games/search",
                    roadmap: "/games/roadmap",
                    playPromoGame: "/games/play/promo",
                    getMarketingMaterials: "/games/marketing-materials/:gameId"
                },
                project: {
                    health: "/project/health",
                    newsList: "/project/news-list",
                    newsArticle: "/project/news-article/:newsId",
                    websiteFooter: "/project/website-footer"
                },
                site: {
                    getFunGameUrl: "/site/fun-game-url/:gameCode"
                },
                path: "/api"
            }
        },
        revalidation: {
            projects: {
                site: {
                    default: { sMaxAgeSeconds: 60, staleWhileRevalidate: 300 } // 5 min
                },
                pa: {
                    default: { sMaxAgeSeconds: 60, staleWhileRevalidate: 120 } // 2 min
                }
            }
        }
    },

    systemSettings: {
        defaultCurrencyCode: EnumCurrencyCode.USD,
        defaultLanguageCode: EnumLanguageCode.en,
        defaultRegionCode: EnumRegionCode.eu
    },

    hashids: {
        salt: "PartnerArea Project",
        alphabet: hashidsAlphabet,
        alphabetLength: 12
    },

    password: {
        saltOrRounds: 12 // The salt to be used in encryption. If specified as a number then a salt will be generated
        // with the specified number of rounds and used.
    },

    gameServer: <IConfigGameServer>{
        available: [
            {
                regionCode: EnumRegionCode.china,
                googleBucketKey: "asia",
                active: true,
                currencyCode: EnumCurrencyCode.CNY,
                currencySymbol: "¥",
                entities: [
                    {
                        name: "China",
                        entity: {
                            key: "13ddf17b-f778-4488-acce-67d539ee2653",
                            username: "Skywindgroup_user",
                            type: EnumGameServerEntityType.production,
                            playMode: EnumGameServerPlayMode.fun,
                            token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjoxNTAsInRzIjoxNTI3ODI0NDAzMTUyLCJpYXQiOjE1Mjc4MjQ0MDMsImlzcyI6InNreXdpbmRncm91cCJ9.l0fAG7jSqkOUr858kTYe5OkoTusluiVYMtIt5aJhz0whhk6oCfeodDYEAVMrJZo3V1jO_zpZYifYBKjGpNbBOA"
                        },
                        api: {
                            site: { url: "https://api-site.m27613.com/v1" },
                            player: { url: "https://api-player.m27613.com/v1" }
                        }
                    }
                ]
            },
            {
                regionCode: EnumRegionCode.asia,
                googleBucketKey: "asiaskywind",
                active: true,
                currencyCode: EnumCurrencyCode.CNY,
                currencySymbol: "¥",
                entities: [
                    {
                        name: "Asia",
                        entity: {
                            key: "540bfaf7-2d84-4e2e-92fe-cc4a9727b926",
                            username: "asiaskywindcom_user",
                            type: EnumGameServerEntityType.production,
                            playMode: EnumGameServerPlayMode.fun,
                            token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjo1MDg2LCJ0cyI6MTYwMDc3ODIxMDQxMywiaWF0IjoxNjAwNzc4MjEwLCJpc3MiOiJza3l3aW5kZ3JvdXAifQ.FzqAWPk3oA8XToEM5SHYMfpsiKz8KI0dOFl6mmUurJb-jbJEiQkxoeH3vxGFpThVN9QWQR7rpUGRrruDq4TAWA"
                        },
                        api: {
                            site: { url: "https://api-site.m27613.com/v1" },
                            player: { url: "https://api-player.m27613.com/v1" }
                        }
                    }
                ]
            },
            {
                regionCode: EnumRegionCode.eu,
                googleBucketKey: "eu",
                active: true,
                currencyCode: EnumCurrencyCode.EUR,
                currencySymbol: "€",
                entities: [
                    {
                        name: "Europe",
                        entity: {
                            key: "9fad0ab8-6943-4f98-b6cd-2c7940b6be9d",
                            username: "Fun_User",
                            type: EnumGameServerEntityType.production,
                            playMode: EnumGameServerPlayMode.fun,
                            token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjo0MSwidHMiOjE1NDgxNjYxMjg1NTksImlhdCI6MTU0ODE2NjEyOCwiaXNzIjoic2t5d2luZGdyb3VwIn0.oB4nKfdJjQqLU3WMKEFvfIEO142ONww5LFlLb2W33nsAxVogIxlsM_1xaneI55PZmAVHknvboKJLOtStUgeKng"
                        },
                        api: {
                            site: { url: "https://site-eu.sw420101.com/v1" },
                            player: { url: "https://player-eu.sw420101.com/v1" }
                        }
                    },
                    {
                        name: "Spain",
                        entity: {
                            key: "06401ccc-28d4-4f21-824f-7c3a242bfa8d",
                            username: "Fun_User",
                            type: EnumGameServerEntityType.production,
                            playMode: EnumGameServerPlayMode.fun,
                            token: "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjo2OTgsInRzIjoxNTk1NDk2MzcyMjU4LCJpYXQiOjE1OTU0OTYzNzIsImlzcyI6InNreXdpbmRncm91cCJ9.vlJP-cDwhLEIwY3iTMkwka2E4FluKgoIjA99X8xb6PbkZQx1yGSJfC0fWKYe6bnuxPIxNfscEun569x1XXqHLQ"
                        },
                        api: {
                            site: { url: "https://site-eu.sw420101.com/v1" },
                            player: { url: "https://player-eu.sw420101.com/v1" }
                        }
                    },
                    {
                        name: "Europe pre-production",
                        entity: {
                            key: "b0685b57-cedc-4385-99ef-013691e4d174",
                            username: "sws_admin1",
                            password: "CjnUvRnfC9pEQK8z",
                            type: EnumGameServerEntityType.staging,
                            playMode: EnumGameServerPlayMode.real
                        },
                        api: {
                            operator: { url: "https://operator-eu.ss211208.com/v1" }
                        }
                    }
                ]
            }
        ],
        default: {
            regionCode: EnumRegionCode.eu
        }
    }
};

export default appConfig;

export type TAppConfig = typeof appConfig;
