import { EnumEnvironment, EnumServerEnv } from "models/enum/system";
import { EnumDBProviders } from "models/enum/db";

const appServerConfig = {
    environment: process.env.NODE_ENV || EnumEnvironment.develop, // this is set by nextjs cant be overwritten

    serverEnv: process.env.SERVER_ENV || EnumServerEnv.local, // server

    server: {
        webServicesApi: {
            url: process.env.WEB_SERVICES_API_URL || (
                process.env.SERVER_ENV === EnumServerEnv.production
                ? "/api/prod"
                : (process.env.SERVER_ENV === EnumServerEnv.develop
                   ? "/api/dev"
                   : "/api/local"))
        },
    },

    smtp: {
        // For local generate https://ethereal.email/create
        // host: process.env.SMTP_HOST || "email-smtp.us-east-1.amazonaws.com",
        // port: process.env.SMTP_PORT || 465,
        // secure: process.env.SMTP_SECURE ? JSON.parse(process.env.SMTP_SECURE) : true,
        // requireTLS: process.env.SMTP_REQUIRE_TLS ? JSON.parse(process.env.SMTP_REQUIRE_TLS) : false,
        // auth: {
        //     user: process.env.SMTP_USER || "AKIAJA7A4LLOZX6LSTOA",
        //     pass: process.env.SMTP_PASSWORD || "AmwTOS4Zy5jRPIH6xut+dLDnEls+MW7KjVMMmn0H0njR",
        // },
        // host: 'in-v3.mailjet.com',
        // port: '587',
        // secure: false,
        // auth: {
        //     user: '338d1a7e26cea0d563efdabf7f6a577c',
        //     pass: '426e9f8d32437df1d1a3a0f602dba054',
        // }
        host: process.env.SMTP_HOST || "smtp.ethereal.email",
        port: process.env.SMTP_PORT || 587,
        secure: process.env.SMTP_SECURE ? JSON.parse(process.env.SMTP_SECURE) : false,
        requireTLS: process.env.SMTP_REQUIRE_TLS ? JSON.parse(process.env.SMTP_REQUIRE_TLS) : true,
        auth: {
            user: process.env.SMTP_USER || "<EMAIL>",
            pass: process.env.SMTP_PASSWORD || "8p2buXuNApY4XdjcpR"
        }
    },

    jwt: {
        access: {
            // used only for authorization with username and password
            secretKey: process.env.JWT_ACCESS_SECRET_KEY || "aSUHCCZxZUAYkg2Enid1suAvy3bRCDJBCrtb13ZdIY5Dq9Eb2RaSU",
            expiresIn: "30m",
            cookie: {
                options: {
                    maxAge: 30 * 60 * 1000 // 30 min
                }
            }
        },
        refresh: {
            secretKey: process.env.JWT_REFRESH_SECRET_KEY || "AYUAYkkg2Enid1s12RaSU3Zg2EniddIYZdIY55id1suDq9Eb9Eb2R",
            expiresIn: "30d",
            cookie: {
                options: {
                    maxAge: 30 * 24 * 60 * 60 * 1000 // 30 days
                }
            }
        }
    },

    sanity: {
        client: {
            projectId: process.env.SANITY_CLIENT_PROJECT_ID || "usfaerfs",
            dataset: process.env.SANITY_CLIENT_DATASET || "production",
            token: process.env.SANITY_CLIENT_TOKEN || "skSe86CCpb5t8hesiX8513bR",
            apiVersion: process.env.SANITY_CLIENT_PROJECT_ID || new Date().toISOString().substring(0, 10) // use a UTC
            // date
            // string
        }
    },

    db: {
        provider: EnumDBProviders.mysql,
        mysql: {
            write: {
                database: process.env.DB_API_WRITE_DATABASE || "dev-play-skywindgroup-com",
                user: process.env.DB_API_WRITE_USER || "dev-play-user",
                password: process.env.DB_API_WRITE_PASSWORD || "24;4i7RC{79LA4RkH,8d",
                host: process.env.DB_API_WRITE_HOST || "**********",
                port: Number(process.env.DB_API_WRITE_PORT) || 3306
            }
        }
    },
    logging: {
        graylog: {
            /*process.env.SERVER_ENV === String(EnumServerEnv.local) ? null : */
            host: process.env.GRAYLOG_HOST || "***********",
            /*process.env.SERVER_ENV === String(EnumServerEnv.local) ? null : */
            port: Number(process.env.GRAYLOG_PORT) || 12202
        },
        kafka: {
            highWaterMark: Number(process.env.KAFKA_LOGGING_HIGHWATER_MARK) || 1024,
            recreateStreamTimeout: Number(process.env.KAFKA_LOGGING_STREAM_RECREATE_TIMEOUT) || 1000,
            kafkaHost: process.env.KAFKA_LOGGIN_HOST || "kafkaserver:9092",
            requestTimeout: Number(process.env.KAFKA_LOGGING_REQUEST_TIMEOUT) || 1000,
            connectionTimeout: Number(process.env.KAFKA_LOGGING_CONNECT_TIMEOUT) || 1000,
            requireAcks: Number(process.env.KAFKA_LOGGING_REQUIRE_ACK) || 0,
            ackTimeoutMs: Number(process.env.KAFKA_LOGGING_ACK_TIMEOUT) || 0,
            partitionerType: Number(process.env.KAFKA_LOGGING_PARTITIONER_TYPE) || 1,
            topic: process.env.KAFKA_LOGGING_TOPIC || "sw-logging"
            // host: process.env.KAFKA_LOGGING_HOST || "***********:9092,***********:9092",
            // topic: process.env.KAFKA_LOGGING_TOPIC || "sw-logging-public-partner-area-stg",
        },

        logLevel: process.env.LOG_LEVEL || "debug",

        loggingOutputType: (process.env.LOGGING_OUTPUT_TYPE || "console") as "kafka" | "console" | "graylog",

        queryLogging: process.env.LOGGING_QUERY !== "false",

        logSuccessResponse: process.env.LOGGING_LOG_SUCCESS !== "false"
    },

    cdnDomain: process.env.CDN_DOMAIN || "",

    siteApi: {
        eu: {
            domain: process.env.SITE_API_EU_DOMAIN || "https://api-site.m27613.com/v1",
            token: process.env.SITE_API_EU_TOKEN || "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjo0MSwidHMiOjE1NDgxNjYxMjg1NTksImlhdCI6MTU0ODE2NjEyOCwiaXNzIjoic2t5d2luZGdyb3VwIn0.oB4nKfdJjQqLU3WMKEFvfIEO142ONww5LFlLb2W33nsAxVogIxlsM_1xaneI55PZmAVHknvboKJLOtStUgeKng"
        },
        asia: {
            domain: process.env.SITE_API_ASIA_DOMAIN || "https://api-site.m27613.com/v1",
            token: process.env.SITE_API_ASIA_TOKEN || "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJicmFuZElkIjo1MDg2LCJ0cyI6MTYwMDc3ODIxMDQxMywiaWF0IjoxNjAwNzc4MjEwLCJpc3MiOiJza3l3aW5kZ3JvdXAifQ.FzqAWPk3oA8XToEM5SHYMfpsiKz8KI0dOFl6mmUurJb-jbJEiQkxoeH3vxGFpThVN9QWQR7rpUGRrruDq4TAWA"
        }
    }
}

export type IAppServerConfig = typeof appServerConfig;
export default appServerConfig;
