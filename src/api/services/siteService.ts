import logger from "../../utils/logger";
import axios, { AxiosResponse } from "axios";
import { ISiteGameUrlResponse, ISiteServerService } from "./interfaces/iSiteServerService";
import appServerConfig from "appServerConfig";
import { NextApiRequest } from "next";
import { EnumUserAction } from "models/enum/user";
import { IUserActivityParams, IUserInfo } from "./interfaces/iUsersService";
import { DBProviderInstance } from "api/data-providers/db/dbProvider";
import { UsersServiceInstance } from "./usersService";
import * as Errors from "utils/errors";
import { errorCodes } from "errorCodes";
import ServerService from "./serverService";

const log = logger("service--site-server");

export class SiteService extends ServerService implements ISiteServerService {

    public async getFunGameUrl(req: NextApiRequest, gameCode: string): Promise<void | ISiteGameUrlResponse> {
        let activityAttrs = await DBProviderInstance.activityPrepare(
            EnumUserAction.getFunGameUrl,
            { params: { gameCode } } as IUserActivityParams,
            req
        );

        try {
            const user = (await UsersServiceInstance.getAuthorisedUser(req)) as IUserInfo;
            if (!user) {
                const error = new Errors.BadRequest(errorCodes.e20007001);
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }
            activityAttrs = DBProviderInstance.activitySetUser(activityAttrs, user.id);

            if (!gameCode || gameCode.trim().length === 0) {
                const error = new Errors.BadRequest(errorCodes.e20007002);
                activityAttrs.game_code = gameCode;
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            const response = await axios.get<unknown, AxiosResponse<ISiteGameUrlResponse>>(
                `${appServerConfig.siteApi.eu.domain}/fun/games/${gameCode}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json",
                        "X-ACCESS-TOKEN": appServerConfig.siteApi.eu.token
                    }
                }
            );

            if (!response.data || !response.data.url) {
                const error = new Errors.NotFound(errorCodes.e20007003);
                activityAttrs.game_code = gameCode;
                await DBProviderInstance.activityFailed(activityAttrs, error);
                return Promise.reject(error);
            }

            await DBProviderInstance.activitySuccess(activityAttrs);
            return response.data;
        } catch (e) {
            return Promise.reject(
                await this.handleServiceError(req, log, e, "SiteService.getFunGameUrl", activityAttrs)
            );
        }
    }
}

export const SiteServiceInstance = new SiteService();