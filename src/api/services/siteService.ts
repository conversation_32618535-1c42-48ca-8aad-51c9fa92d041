import logger from "../../utils/logger";
import axios, { AxiosError, AxiosResponse } from "axios";
import { ISiteGameUrlResponse, ISiteServerService } from "./interfaces/iSiteServerService";
import appServerConfig from "appServerConfig";

const log = logger("service--site-server");

export class SiteService implements ISiteServerService {

    public async getGameUrl(gameCode: string): Promise<ISiteGameUrlResponse | AxiosError | Error> {
        try {
            const response = await axios.get<unknown, AxiosResponse<ISiteGameUrlResponse>>(
                `${appServerConfig.siteApi.eu.domain}/fun/games/${gameCode}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        accept: "application/json",
                        "X-ACCESS-TOKEN": appServerConfig.siteApi.eu.token
                    }
                }
            );
            return response.data;
        } catch (e) {
            log.error(e, "Error at SiteService:getGameUrl");
            return e as Error;
        }
    }
}

export const SiteServiceInstance = new SiteService();