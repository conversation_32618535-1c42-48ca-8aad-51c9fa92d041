# getFunGameUrl Implementation Summary

## Overview
This implementation follows the same architectural patterns as the existing `getMarketingKit` functionality in the SiteService, providing a consistent and robust solution for fetching fun game URLs from external APIs.

## Architecture Flow

### 1. Client Layer
- **File**: `src/client/services/clientSiteService.ts`
- **Method**: `ClientSiteService.getFunGameUrl(gameCode: string)`
- **Purpose**: Handles client-side API calls with proper error handling and response processing

### 2. API Controller Layer
- **File**: `src/api/controllers/siteController.ts`
- **Method**: `SiteController.getFunGameUrl(params: { gameCode: string })`
- **Route**: `/api/site/fun-game-url/:gameCode`
- **Purpose**: Validates request parameters and coordinates with service layer

### 3. Service Layer
- **File**: `src/api/services/siteService.ts`
- **Method**: `SiteService.getFunGameUrl(req: NextApiRequest, gameCode: string)`
- **Purpose**: Implements business logic, user authentication, activity logging, and external API integration

### 4. API Route
- **File**: `src/pages/api/site/[[...path]].ts`
- **Purpose**: Next.js API route handler that delegates to SiteController

## Key Features Implemented

### 1. User Authentication
- Requires authenticated user (similar to getMarketingKit)
- Uses `UsersServiceInstance.getAuthorisedUser(req)` for validation
- Returns appropriate error codes for unauthorized access

### 2. Activity Logging
- Tracks all requests with `EnumUserAction.getFunGameUrl`
- Logs success and failure scenarios
- Associates activities with specific users and game codes

### 3. Input Validation
- Validates game code parameter using Yup schema
- Ensures game code is provided and not empty
- Returns structured error responses for invalid inputs

### 4. Error Handling
- **e20007001**: User not found or disabled
- **e20007002**: Game code required
- **e20007003**: Game URL not found
- **e12007001**: Controller-level error handling

### 5. External API Integration
- Makes authenticated requests to `${appServerConfig.siteApi.eu.domain}/fun/games/${gameCode}`
- Uses proper headers including X-ACCESS-TOKEN
- Handles API response validation

### 6. Response Structure
```typescript
interface ISiteGameUrlResponse {
    url: string;
    token: string;
    currency: string;
}
```

## Configuration Changes

### 1. Endpoint Configuration
Added to `src/appConfig.ts`:
```typescript
site: {
    getFunGameUrl: "/site/fun-game-url/:gameCode"
}
```

### 2. Validation Schema
Added to `src/models/validatorSchemas.ts`:
```typescript
game: {
    gameId: yup.string().trim().max(64),
    gameCode: yup.string().trim().max(64), // NEW
}
```

### 3. User Action Enum
Added to `src/models/enum/user.ts`:
```typescript
getFunGameUrl = "get-fun-game-url"
```

### 4. Error Codes and Messages
Added error definitions and localized messages for proper error handling.

## Usage Example

### Component Usage
```typescript
import { ClientSiteService } from "client/services/clientSiteService";

const gameUrlData = await ClientSiteService.getFunGameUrl("sw_game_001");
if (gameUrlData && gameUrlData.url) {
    // Use the game URL, token, and currency
    window.open(gameUrlData.url, '_blank');
}
```

### Test Page
A complete test page is available at `src/pages/test-fun-game-url.tsx` demonstrating:
- Form input for game code
- Real-time API calls
- Response display
- Error handling

## Consistency with getMarketingKit

This implementation maintains consistency with the existing `getMarketingKit` functionality by:

1. **Same Service Inheritance**: Extends `ServerService` for common error handling
2. **Same Authentication Pattern**: Requires authenticated users
3. **Same Activity Logging**: Uses `DBProviderInstance` for activity tracking
4. **Same Error Handling**: Follows established error code patterns
5. **Same Client Service Pattern**: Mirrors `ClientGamesService.getMarketingKit` structure
6. **Same Validation Approach**: Uses Yup schemas for parameter validation

## Files Modified/Created

### Modified Files
- `src/api/services/siteService.ts` - Enhanced with new method
- `src/api/services/interfaces/iSiteServerService.ts` - Updated interface
- `src/models/enum/user.ts` - Added new user action
- `src/models/validatorSchemas.ts` - Added game code validator
- `src/appConfig.ts` - Added endpoint configuration
- `src/errorCodes.ts` - Added error codes
- `src/locales/enumLocale.ts` - Added error enum values
- `src/locales/en-US.ts` - Added error messages

### Created Files
- `src/api/controllers/siteController.ts` - New controller
- `src/pages/api/site/[[...path]].ts` - API route
- `src/client/services/clientSiteService.ts` - Client service
- `src/client/components/fun-game-url/funGameUrl.tsx` - Demo component
- `src/pages/test-fun-game-url.tsx` - Test page

## Testing
The implementation can be tested using the test page at `/test-fun-game-url` which provides:
- Interactive form for game code input
- Real-time API testing
- Response visualization
- Error scenario handling

This implementation provides a production-ready solution that follows established patterns and maintains consistency with the existing codebase architecture.
