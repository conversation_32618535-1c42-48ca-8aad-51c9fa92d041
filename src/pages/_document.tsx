import * as React from "react";
// eslint-disable-next-line @next/next/no-document-import-in-page
import Document, { DocumentContext, DocumentInitialProps, Head, Html, Main, NextScript } from "next/document";
import { Project } from "utils/project";
import { EnumProjectType } from "models/enum/system";

class AppDocument extends Document {

    static isSite = false;
    static host = "none";

    static async getInitialProps(context: DocumentContext): Promise<DocumentInitialProps> {
        const initialProps = await Document.getInitialProps(context);

        // To make ctx.req contain the expected payload, you must invoke getServerSideProps further down the Next.js
        // tree, for example, in pages/index.js. Then ctx.req in _document will no longer be undefined.
        const host = context.req?.headers.host;

        const project = Project.getProjectByHost(String(host));
        // if (project?.type === EnumProjectType.site) {
        //     AppDocument.isSite = true;
        // }

        AppDocument.host = String(host);

        AppDocument.isSite = (project?.type === EnumProjectType.site);

        return initialProps;
    }

    render() {
        // noinspection HtmlRequiredTitleElement
        return (
            <Html lang="en">
                <Head>
                    {AppDocument.isSite ? (
                        <link rel="stylesheet" href={"/styles/site.main.css?v=1"} />
                    ) : (
                         <link rel="stylesheet" href={"/styles/pa.main.css"} />
                     )}
                    {!AppDocument.isSite && (
                        <link
                            href="https://fonts.googleapis.com/css2?family=Noto+Sans+Display:wght@400;600;900&family=Roboto:wght@400;700&display=swap"
                            rel="stylesheet"
                        />
                    )}
                </Head>
                <body>
                <Main />
                <NextScript />
                </body>
            </Html>
        );
    }
}

// noinspection JSUnusedGlobalSymbols
export default AppDocument;
