import * as yup from "yup";
import { object } from "yup";
import Yup<PERSON>assword from "yup-password";
import { ValidateOptions } from "yup/lib/types";
import { ObjectShape } from "yup/lib/object";
import { EnumUserClientAction } from "./enum/user";
import { EnumCurrencyCode, EnumProjectCode } from "models/enum/system";

YupPassword(yup); // extend yup

const newPassword = yup
    .string()
    .password()
    .minSymbols(1, 'Passwords must contain a minimum of 1 special character: ~`!@#$%^&*()-_+={}[]|\\;:"<>,./?')
    .minRepeating(2)
    .minNumbers(1)
    .minLowercase(1)
    .minUppercase(1)
    .min(10)
    .max(20);

const currentPassword = yup.string().min(10).max(20);

export const validator = {
    contactUs: {
        message: yup.string().trim().min(50).max(65000),
    },
    user: {
        // IUserRegister
        login: yup.string().trim().min(10),
        name: yup.string().trim().max(100),
        phone: yup.string().max(100),
        playerCode: yup
            .string()
            .matches(/^[A-Za-z0-9\-_]+$/, "Only alphabets, numbers are allowed for this field ")
            .min(6)
            .max(32),
        company: yup.string().trim().max(200),
        companyPosition: yup.string().trim().max(200),
        countryCode: yup.string().trim().min(2).max(3),
        email: yup.string().trim().lowercase().email().max(255),
        currentPassword: currentPassword,
        firstEntranceAgreement: yup.boolean(),
        newPassword: newPassword.notOneOf(
            [yup.ref("currentPassword"), null],
            "Your new password must be different from your previous password"
        ),
        repeatPassword: newPassword.when("newPassword", {
            is: (val: string) => !!(val && val.length > 0),
            then: yup.string().oneOf([yup.ref("newPassword")], "Both password need to be the same"),
        }),
        firstName: yup.string().trim().max(100),
        lastName: yup.string().trim().max(100),
        activationHash: yup.string(),
    },
    app: {
        currencyCode: yup.string().oneOf(Object.values(EnumCurrencyCode)),
        projectCode: yup.string().oneOf(Object.values(EnumProjectCode))
    },
    clientAction: {
        action: yup.string().oneOf(Object.values(EnumUserClientAction)),
        fromPageUrl: yup.string(),
        fromArea: yup.string(),
        params: yup.object(),
    },
    game: {
        gameId: yup.string().trim().max(64),
        gameCode: yup.string().trim().max(64),
    },
    news: {
        newsId: yup.string().trim().max(64),
    },
    gitbook: {
        space: yup.string().max(128),
    },
    storage: {
        newsId: yup.string().max(64),
    },
};

export async function validateRequestParams<T>(params: ObjectShape, data: T): Promise<T> {
    return (await object(params).validate(data, {
        /** @see https://www.npmjs.com/package/yup#mixedvalidatevalue-any-options-object-promiseany-validationerror */
        strict: false, // strict: only validate the input, and skip any coercion or transformation
        abortEarly: true, // abortEarly: return from validation methods on the first error rather than after all
        // validations run.
        stripUnknown: true, // stripUnknown: remove unspecified keys from objects.
        recursive: true, // recursive: when false validations will not descend into nested schema (relevant for objects
        // or arrays).
        // context?: object; // context: any context needed for validating schema conditions (see: when())
    } as ValidateOptions)) as T;
}
