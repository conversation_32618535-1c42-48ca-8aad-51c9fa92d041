/***
 * MAXIMUM LENGTH IS 40 characters
 */

export enum EnumActivityStatus {
    success = "success",
    failed = "failed",
    serverError = "server-err",
}

export enum EnumUserClientAction {
    pageRoadmap = "page-roadmap",
    pageRoadmapSkywind = "page-roadmap-skywind",
    pageRoadmapSlotFactory = "page-roadmap-slot-factory",
    pageLiveGames = "page-live-games",
    pageNews = "page-news",
    pageNewsInfo = "page-news-info",
    pageSearch = "page-search",
    pageMyAccount = "page-my-account",
    pageSupportDocs = "page-support-docs",
    pageTournament = "page-tournament",
    playTournament = "play-tournament",
    tournamentSurvey = "tournament-survey",
    getGameUrlTournament = "get-game-url-tournament",
    rulesTournament = "rules-tournament",
    pageUsersList = "page-users-list",
    pageFreeBets = "page-free-bets",
    pageMustWinJackpot = "page-must-win-jackpot",
    pageTournaments = "page-tournaments",
    pageLuckyEnvelopes = "page-lucky-envelopes",
    roadmapAvailable = "roadmap-available",
    roadmapLatest = "roadmap-latest",
    roadmapUpcoming = "roadmap-upcoming",
    roadmapUpcomingMarket = "roadmap-upcoming-market",
    roadmapUpcomingMonth = "roadmap-upcoming-month",
    gamePlay = "game-play-game",
    gameInfoSheet = "game-info-sheet",
    gamePromoVideo = "game-promo-video",
    gamePage = "game-page",
    gameMarketingBrowse = "game-marketing-browse",
    gameMarketingDownload = "game-marketing-download",
    gameProductSheet = "game-product-sheet",
    gameCertificates = "game-certificates",
    bannerTop = "banner-top",
    setPlayerCode = "set-player-code",
    search = "search",
    userChangePassword = "user-change-password",
    userForgotPassword = "user-forgot-password",
    userLogin = "user-login",
    userLogout = "user-logout",
    userRegister = "user-register",
    userProfileUpdate = "user-profile-update",
}

export enum EnumUserClientArea {
    gamesTable = "gamesTable",
    gamePage = "gamePage",
}

export enum EnumUserAction {
    userRegistration = "user-registration",
    userGetAuthorised = "get-authorised",
    userRefreshToken = "user-refresh-token",
    userActivation = "user-activation",
    userLogin = "user-login",
    userSetPlayerCode = "user-set-player-code",
    userGetPlayerCode = "user-get-player-code",
    userChangePassword = "user-change-password",
    userSaveAttribute = "user-save-attribute",
    userLogout = "user-logout",
    getAvailableGames = "get-available-games",
    getSearchGames = "get-search-games",
    getTotalJackpots = "get-total-jackpots",
    getRoadmapGames = "get-roadmap-games",
    getPromoGameUrl = "get-promo-game-url",
    getFunGameUrl = "get-fun-game-url",
    getMarketingKit = "get-marketing-kit",
    getNewsList = "get-news-list",
    getNewsInfo = "get-news-info",
    getWebsiteFooter = "get-website-footer",
    getGitbookSpaceUrl = "get-gitbook-space-url",
}
