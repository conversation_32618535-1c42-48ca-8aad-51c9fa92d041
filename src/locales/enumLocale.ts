import { IComponentSearch } from "./interfaces/iComponentSearch";

export enum EnumError {
    userPermissionDenied = "userPermissionDenied",
    anAccountWithThisEmailAlreadyExists = "anAccountWithThisEmailAlreadyExists",
    userIsNotFound = "userIsNotFound",
    userInAccessToken = "userInAccessToken",
    userNotFoundOrDisabled = "userNotFoundOrDisabled",
    tournamentDisabledOrEndedOrNotStarted = "tournamentDisabledOrEndedOrNotStarted",
    cantGenerateGameUrl = "cantGenerateGameUrl",
    playerCodeAlreadySet = "playerCodeAlreadySet",
    cantConnectToGameServer = "cantConnectToGameServer",
    invalidPlayerCode = "invalidPlayerCode",
    playerCodeExists = "playerCodeExists",
    impossibleSetPlayerBalance = "impossibleSetPlayerBalance",
    userNotFoundOrDisabledOrNoPlayerCode = "userNotFoundOrDisabledOrNoPlayerCode",
    userNotActivated = "userNotActivated",
    internalServerError = "internalServerError",
    wrongPassword = "wrongPassword",
    userActivationNotFound = "userActivationNotFound",
    userRefreshTokenNotFound = "userRefreshTokenNotFound",
    newPasswordDoesntMatchCurrent = "newPasswordDoesntMatchCurrent",
    gameNotFound = "gameNotFound",
    contentNotFound = "contentNotFound",
    marketingMaterialsNotFound = "marketingMaterialsNotFound",
    gitbookSpaceNotFound = "gitbookSpaceNotFound",
    gameCodeRequired = "gameCodeRequired",
    gameUrlNotFound = "gameUrlNotFound",
}

export enum EnumMessage {
    skywindCompanyName = "skywindCompanyName",
    headerMenuGames = "headerMenuGames",
    headerMenuRoadMap = "headerMenuRoadMap",
    headerMenuAllGames = "headerMenuAllGames",
    headerMenuRoadMapSkywind = "headerMenuRoadMapSkywind",
    headerMenuRoadMapSlotFactory = "headerMenuRoadMapSlotFactory",
    headerMenuLiveGames = "headerMenuLiveGames",
    headerMenuProfile = "headerMenuProfile",
    headerMenuLogout = "headerMenuLogout",
    headerMenuNews = "headerMenuNews",
    headerMenuSearch = "headerMenuSearch",
    notApplicable = "notApplicable",

    buttonLearnMore = "buttonLearnMore",
    buttonComingSoon = "buttonComingSoon",
    buttonAllGames = "buttonAllGames",

    gameRTP = "gameRTP",
    gameVolatility = "gameVolatility",

    titlePlayGame = "titlePlayGame",
}

export type IEnumLocale = Partial<Record<EnumError | EnumMessage, string>>;

export interface ILocaleObject {
    errors: IEnumLocale;
    messages: IEnumLocale;
    components: {
        search: IComponentSearch;
    };
}
