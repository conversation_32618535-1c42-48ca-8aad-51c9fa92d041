import logger from "utils/logger";
import { NextApiRequest, NextApiResponse } from "next";
import appConfig from "appConfig";
import { ServerRequest } from "utils/serverRequest";
import { ServerController } from "api/controllers/serverController";
import { EnumServerResponseType } from "utils/serverResponse";
import { errorCodes } from "errorCodes";
import { validateRequestParams, validator } from "models/validatorSchemas";
import { SiteServiceInstance } from "api/services/siteService";

const log = logger("controller--site");

export class SiteController extends ServerController {
    constructor(serverRequest: ServerRequest) {
        super(serverRequest);
    }

    static async handler(req: NextApiRequest, res: NextApiResponse): Promise<void> {
        try {
            const endpoints = appConfig.client.endpoints.api;
            const c = new ServerRequest(SiteController, req, res, log, appConfig.client.endpoints.api.path);
            await c.httpGet(endpoints.site.getFunGameUrl, SiteController.prototype.getFunGameUrl, true);
            await c.httpEnd();
        } catch (e) {
            log.error(e);
        }
    }

    async getFunGameUrl(params: { gameCode: string }): Promise<void> {
        try {
            const data = await validateRequestParams<{ gameCode: string }>(
                {
                    gameCode: validator.game.gameCode.required()
                },
                params
            );
            const response = await SiteServiceInstance.getFunGameUrl(this.request.req, data.gameCode);
            this.successResponse(
                response,
                EnumServerResponseType.successObject,
                SiteController.prototype
            ).serverResponse();
        } catch (e) {
            this.errorResponseForGet(e as Error, SiteController.prototype, errorCodes.e12007001);
        }
    }
}
