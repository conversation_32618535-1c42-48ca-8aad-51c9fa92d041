name: partner-area-service
namespace: sw-internal

labels:
  k8s-app: partner-area-service

replicaCount: 2

image:
  repository: asia.gcr.io/gcpstg/sw-partner-area-service
  tag: release_prod-env
  pullPolicy: Always 
  # checksum: sha256:db506d2739c5b5db9b421fbf5866b2fabbb7422685f181a5f88a152e74f89025
  # pullPolicy: IfNotPresent 

containerPorts:
  - 3044:http

clusterIP: *************
serviceType: ClusterIP
servicePorts:
  - 80:3044:http

hostAliases:
  - ro-dc-elk-kafka-01:***********
  - ro-dc-elk-kafka-02:***********
  - ro-dc-elk-kafka-03:***********
  - ro-dc-elk-kafka-04:***********
  - ro-dc-elk-kafka-05:***********

serviceAccount: partner-area-service-acc
serviceAccountName: partner-area-service-acc

rollingUpdate:
  maxSurge: 1
  maxUnavailable: 0

readinessProbe:
  httpGet:
    path: /
    port: http
  initialDelaySeconds: 15
  periodSeconds: 10  

resources:
  limits:
    cpu: "2"
    memory: 4Gi
  requests:
    cpu: 100m
    memory: 2Gi  
