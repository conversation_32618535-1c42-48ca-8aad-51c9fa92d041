import React, { useState } from "react";
import { observer } from "mobx-react-lite";
import { NextPage } from "next";
import FunGameUrl from "@components/fun-game-url/funGameUrl";

/**
 * Test page to demonstrate the getFunGameUrl functionality
 * This page shows how to use the new SiteService.getFunGameUrl method
 * similar to how getMarketingKit is used
 */
const TestFunGameUrlPage: NextPage = observer(() => {
    const [gameCode, setGameCode] = useState<string>("");
    const [submittedGameCode, setSubmittedGameCode] = useState<string>("");

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (gameCode.trim()) {
            setSubmittedGameCode(gameCode.trim());
        }
    };

    return (
        <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
            <h1>Test Fun Game URL Generator</h1>
            <p>
                This page demonstrates the new <code>getFunGameUrl</code> functionality 
                implemented in SiteService, following the same patterns as <code>getMarketingKit</code>.
            </p>

            <div style={{ marginBottom: "30px" }}>
                <h2>How it works:</h2>
                <ol>
                    <li><strong>Client Service:</strong> ClientSiteService.getFunGameUrl() calls the API</li>
                    <li><strong>API Controller:</strong> SiteController.getFunGameUrl() handles the request</li>
                    <li><strong>Service Layer:</strong> SiteService.getFunGameUrl() processes the business logic</li>
                    <li><strong>External API:</strong> Makes request to site API to get game URL</li>
                    <li><strong>Response:</strong> Returns game URL, token, and currency information</li>
                </ol>
            </div>

            <form onSubmit={handleSubmit} style={{ marginBottom: "30px" }}>
                <div style={{ marginBottom: "15px" }}>
                    <label htmlFor="gameCode" style={{ display: "block", marginBottom: "5px" }}>
                        Game Code:
                    </label>
                    <input
                        type="text"
                        id="gameCode"
                        value={gameCode}
                        onChange={(e) => setGameCode(e.target.value)}
                        placeholder="Enter game code (e.g., sw_game_001)"
                        style={{ 
                            padding: "8px", 
                            width: "300px", 
                            border: "1px solid #ccc", 
                            borderRadius: "4px" 
                        }}
                    />
                </div>
                <button 
                    type="submit"
                    style={{
                        padding: "10px 20px",
                        backgroundColor: "#007bff",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer"
                    }}
                >
                    Generate Fun Game URL
                </button>
            </form>

            {submittedGameCode && (
                <div>
                    <h3>Result for Game Code: {submittedGameCode}</h3>
                    <FunGameUrl gameCode={submittedGameCode} />
                </div>
            )}

            <div style={{ marginTop: "40px", padding: "20px", backgroundColor: "#f8f9fa", borderRadius: "4px" }}>
                <h3>Implementation Details:</h3>
                <ul>
                    <li><strong>Authentication:</strong> Requires user to be logged in (similar to getMarketingKit)</li>
                    <li><strong>Activity Logging:</strong> All requests are logged with user activity tracking</li>
                    <li><strong>Error Handling:</strong> Proper error codes and messages for different failure scenarios</li>
                    <li><strong>Validation:</strong> Game code is validated before making external API call</li>
                    <li><strong>Response Format:</strong> Returns structured data with URL, token, and currency</li>
                </ul>
            </div>
        </div>
    );
});

export default TestFunGameUrlPage;
