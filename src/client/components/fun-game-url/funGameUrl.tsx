import React, { useEffect, useRef, useState } from "react";
import { observer } from "mobx-react-lite";
import { ClientSiteService } from "client/services/clientSiteService";
import { ISiteGameUrlResponse } from "api/services/interfaces/iSiteServerService";

interface IFunGameUrlProps {
    gameCode: string;
}

/**
 * Component to fetch and display fun game URL
 * Similar to MarketingKit component but for game URLs
 */
const FunGameUrl = observer(({ gameCode }: IFunGameUrlProps) => {
    const [gameUrlData, setGameUrlData] = useState<ISiteGameUrlResponse | null>(null);
    const [status, setStatus] = useState<number>(0); // 0: initial, 1: loading, 2: success, 3: error

    const strictModeRef = useRef<boolean>(false);

    useEffect(() => {
        if (!strictModeRef.current && gameCode) {
            strictModeRef.current = true;
            (async () => {
                setStatus(1);

                const data = await ClientSiteService.getFunGameUrl(gameCode);
                if (data && data.url) {
                    setGameUrlData(data);
                    setStatus(2);
                } else {
                    setStatus(3);
                }
            })();
        }
        // eslint-disable-next-line
    }, [gameCode]);

    if (status === 2 && gameUrlData) {
        return (
            <div className="fun-game-url-container">
                <div className="fun-game-url-info">
                    <h4>Fun Game URL Generated</h4>
                    <div className="game-url-details">
                        <div className="url-field">
                            <label>Game URL:</label>
                            <a href={gameUrlData.url} target="_blank" rel="noopener noreferrer">
                                {gameUrlData.url}
                            </a>
                        </div>
                        <div className="token-field">
                            <label>Token:</label>
                            <span>{gameUrlData.token}</span>
                        </div>
                        <div className="currency-field">
                            <label>Currency:</label>
                            <span>{gameUrlData.currency}</span>
                        </div>
                    </div>
                    <div className="action-buttons">
                        <button 
                            className="btn btn-primary"
                            onClick={() => window.open(gameUrlData.url, '_blank')}
                        >
                            Play Game
                        </button>
                        <button 
                            className="btn btn-secondary"
                            onClick={() => navigator.clipboard.writeText(gameUrlData.url)}
                        >
                            Copy URL
                        </button>
                    </div>
                </div>
            </div>
        );
    } else if (status === 1) {
        return <div>Please wait, loading game URL...</div>;
    } else if (status === 3) {
        return <div>Unable to generate game URL for this game</div>;
    } else {
        return <div>Provide a game code to generate URL</div>;
    }
});

export default FunGameUrl;
